<template>
    <div class="bg-[#172333] flex items-center justify-center h-[60px] bg-cover bg-no-repeat">
        <div class="flex relative items-center justify-between w-full max-w-[1920px] px-4">
            <div class="flex items-center justify-end space-x-4">
                <button class="px-4 py-2 text-white transition-all" :class="{
                    'bg-[#1890FF] rounded': currentTab === 'public',
                    'bg-transparent border-none': currentTab !== 'public'
                }" @click="handleTabClick('public')">
                    公共场所在线监测
                </button>
                <button class="px-4 py-2 text-white transition-all" :class="{
                    'bg-[#1890FF] rounded': currentTab === 'radiation',
                    'bg-transparent border-none': currentTab !== 'radiation'
                }" @click="handleTabClick('radiation')">
                    放射卫生在线监测
                </button>
            </div>
            <h1 class="text-xl font-bold text-white absolute left-1/2 -translate-x-1/2">
                漳平市卫生监督管理系统
            </h1>
            <div class="flex items-center justify-start space-x-4">
                <button class="px-4 py-2 text-white transition-all" :class="{
                    'bg-[#1890FF] rounded': currentTab === 'occupational',
                    'bg-transparent border-none': currentTab !== 'occupational'
                }" @click="handleTabClick('occupational')">
                    职业病危害因素在线监测
                </button>
                <button class="px-4 py-2 text-white transition-all" :class="{
                    'bg-[#1890FF] rounded': currentTab === 'video',
                    'bg-transparent border-none': currentTab !== 'video'
                }" @click="handleTabClick('video')">
                    实时视频监控
                </button>
                <button class="px-4 py-2 text-white transition-all" :class="{
                    'bg-[#1890FF] rounded': currentTab === 'support',
                    'bg-transparent border-none': currentTab !== 'support'
                }" @click="handleTabClick('support')">
                    支撑应用管理
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, inject } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const currentTab = ref(''); // 初始选中项，可根据需求调整


// 导航项与路由名称的映射关系
const tabToRouteMap = {
    'public': 'PublicPlaceMonitor',
    'radiation': 'RadiationHealthMonitor',
    'occupational': 'OccupationalHazardMonitor',
    'video': 'RealTimeVideoMonitor',
    'support': 'SupportAppManagement'
};

const handleTabClick = (tab) => {
    // 更新当前选中的标签
    currentTab.value = tab;

    // 获取对应的路由名称
    const routeName = tabToRouteMap[tab];

    // if (routeName) {
    //     // 执行路由跳转
    //     router.push({ name: routeName })
    //         .catch(error => {
    //             console.error('路由跳转失败:', error);
    //             // 可以在这里添加错误处理逻辑，如显示提示信息
    //         });
    // } else {
    //     console.warn(`未找到标签 "${tab}" 对应的路由`);
    // }
};


</script>

<style scoped></style>