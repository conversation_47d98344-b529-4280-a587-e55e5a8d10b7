<template>
    <div class="index-container">
        <!-- 顶部导航栏 -->
        <TopNav />
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 功能入口卡片区域 -->
            <div class="cards-container">
                <div class="function-card" @click="handleClick('public-place')">
                    <div class="icon-circle public-icon">公</div>
                    <span class="card-title">公共场所在线监测</span>
                </div>
                <div class="function-card" @click="handleClick('radiation-health')">
                    <div class="icon-circle radiation-icon">放</div>
                    <span class="card-title">放射卫生在线监测</span>
                </div>
                <div class="function-card" @click="handleClick('occupational-hazard')">
                    <div class="icon-circle occupational-icon">职</div>
                    <span class="card-title">职业病危害因素在线监测</span>
                </div>
                <div class="function-card" @click="handleClick('real-time-video')">
                    <div class="icon-circle video-icon">视</div>
                    <span class="card-title">实时视频监控</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import TopNav from '../components/TopNav.vue'
const router = useRouter()

const handleClick = (type) => {
    // 根据类型跳转不同路由，这里先写死示例，实际按项目路由配置调整
    switch (type) {
        case 'public-place':
            router.push('/public-place-monitor')
            break
        case 'radiation-health':
            router.push('/radiation-health-monitor')
            break
        case 'occupational-hazard':
            router.push('/occupational-hazard-monitor')
            break
        case 'real-time-video':
            router.push('/real-time-video-monitor')
            break
        default:
            break
    }
}
</script>

<style scoped lang="scss">
@import '@/assets/base.scss';

.index-container {
    width: 100%;
    height: 100vh;
    background: $gradient-blue;
}

.main-content {
    @include flex-column;
    @include flex-center;
    height: 100%;
}

.cards-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: $spacing-xl $spacing-md;
}

.function-card {
    width: 256px;
    height: 160px;
    background: $bg-primary;
    border-radius: $border-radius-md;
    box-shadow: $shadow-sm;
    margin: $spacing-sm;
    @include flex-column;
    @include flex-center;
    cursor: pointer;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: $shadow-lg;
    }
}

.icon-circle {
    @include icon-circle(48px);
    margin-bottom: $spacing-sm;
}

.public-icon {
    background-color: $primary-color;
}

.radiation-icon {
    background-color: $success-color;
}

.occupational-icon {
    background-color: $warning-color;
}

.video-icon {
    background-color: $error-color;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: $text-primary;
}
</style>