/* color palette from <https://github.com/vuejs/theme> */

// SCSS 变量定义
$primary-color: #1890FF;
$secondary-color: #172333;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #f5f5f5;
$bg-gray-100: #f5f5f5;
$bg-gray-50: #fafafa;
$bg-blue-50: #e6f7ff;
$bg-blue-100: #bae7ff;

// 渐变背景
$gradient-blue: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);

// 文字颜色
$text-primary: #262626;
$text-secondary: #595959;
$text-disabled: #bfbfbf;
$text-white: #ffffff;

// 边框颜色
$border-color: #d9d9d9;
$border-color-light: #f0f0f0;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

// 圆角
$border-radius-sm: 2px;
$border-radius-md: 4px;
$border-radius-lg: 8px;
$border-radius-full: 50%;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin card-style {
  background: $bg-primary;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  padding: $spacing-md;
}

@mixin button-style($bg-color: $primary-color, $text-color: #ffffff) {
  background-color: $bg-color;
  color: $text-color;
  border: none;
  border-radius: $border-radius-md;
  padding: $spacing-sm $spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    opacity: 0.8;
  }
}

@mixin icon-circle($size: 48px, $bg-color: $primary-color) {
  width: $size;
  height: $size;
  background-color: $bg-color;
  border-radius: $border-radius-full;
  @include flex-center;
  color: $text-white;
  font-weight: bold;
}
