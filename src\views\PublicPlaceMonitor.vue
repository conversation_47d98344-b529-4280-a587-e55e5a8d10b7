<template>
    <div class="monitor-page">
        <h1 class="page-title">公共场所在线监测</h1>
        <ContentTabs />
        <!-- 这里放该页面的静态内容，比如设计图里的图表、数据展示等，用静态图片或简单 HTML 模拟 -->
        <div class="content-area">
            <p class="content-text">公共场所在线监测数据展示区域</p>
            <p class="content-note">（图片资源待添加）</p>
        </div>
    </div>
</template>

<script setup>
import ContentTabs from '../components/ContentTabs.vue'
</script>

<style scoped lang="scss">
@import '@/assets/base.scss';

.monitor-page {
    padding: $spacing-lg;
}

.page-title {
    font-size: 32px;
    margin-bottom: $spacing-md;
    color: $text-primary;
    font-weight: 600;
}

.content-area {
    background-color: $bg-gray-100;
    padding: $spacing-xl;
    border-radius: $border-radius-lg;
    text-align: center;
    margin-top: $spacing-lg;
}

.content-text {
    color: $text-secondary;
    font-size: 16px;
    margin-bottom: $spacing-sm;
}

.content-note {
    font-size: 14px;
    color: $text-disabled;
}
</style>