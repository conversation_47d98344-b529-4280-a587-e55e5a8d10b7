<template>
    <aside class="side-nav">
        <ul class="nav-list">
            <li class="nav-item">
                <router-link to="/public-place-monitor" class="nav-link">公共场所在线监测</router-link>
            </li>
            <li class="nav-item">
                <router-link to="/radiation-health-monitor" class="nav-link">放射卫生在线监测</router-link>
            </li>
            <!-- 其他侧边菜单项，根据设计图对应页面路由写 -->
        </ul>
    </aside>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
</script>

<style scoped lang="scss">
@import '@/assets/base.scss';

.side-nav {
    width: 256px;
    background-color: $bg-gray-100;
    padding: $spacing-md;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: $spacing-sm;
}

.nav-link {
    display: block;
    padding: $spacing-sm $spacing-md;
    color: $text-primary;
    text-decoration: none;
    border-radius: $border-radius-md;
    transition: background-color 0.3s ease;

    &:hover {
        background-color: $bg-secondary;
    }

    &.router-link-active {
        background-color: $primary-color;
        color: $text-white;
    }
}
</style>